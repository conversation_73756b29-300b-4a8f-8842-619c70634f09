{"name": "ts-interface-checker", "version": "0.1.13", "description": "Runtime library to validate data against TypeScript interfaces", "main": "dist/index", "typings": "dist/index", "scripts": {"build": "tsc", "watch": "tsc -w", "test": "tsc && nyc mocha -R list test/", "bench": "tsc && node test/bench/bench.js", "preversion": "npm test", "version": "npm run build"}, "keywords": ["typescript", "ts", "interface", "type", "validate", "validator", "check"], "files": ["dist"], "author": "Dmitry S, Grist Labs", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/gristlabs/ts-interface-checker"}, "bugs": {"url": "https://github.com/gristlabs/ts-interface-checker/issues"}, "nyc": {"extension": [".ts"], "exclude": ["**/*.d.ts"]}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}}